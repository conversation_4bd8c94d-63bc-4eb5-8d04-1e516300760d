# Solution 1: String Content Comparison with Unicode Safety
# Replace the comparison logic in load_existing_pore_data_from_excel()

def is_image_in_current_names(image_name_rich, current_image_names):
    """
    Compare RichString image_name with set of RichString current_image_names
    using string content comparison with Unicode safety for Jython.
    """
    try:
        # Convert the Excel image_name to string safely
        if hasattr(image_name_rich, 'getString'):
            image_str = image_name_rich.getString()
        else:
            image_str = str(image_name_rich)
        
        # Compare with each item in current_image_names
        for current_name_rich in current_image_names:
            try:
                # Convert current name to string safely
                if hasattr(current_name_rich, 'getString'):
                    current_str = current_name_rich.getString()
                else:
                    current_str = str(current_name_rich)
                
                # Direct string comparison
                if image_str == current_str:
                    return True
                    
                # Also try normalized comparison as fallback
                if normalize_filename(image_str) == normalize_filename(current_str):
                    return True
                    
            except Exception as e:
                # If string conversion fails, skip this comparison
                safe_log("Warning: Could not compare with current name: {}".format(str(e)))
                continue
                
        return False
        
    except Exception as e:
        safe_log("Warning: Could not process image name for comparison: {}".format(str(e)))
        return False

# Replace line 225 in the main function with:
# if is_image_in_current_names(image_name, current_image_names):

# Alternative: Modify the main processing to use string sets instead
def create_string_current_names(filenames):
    """Create a set of normalized string names for comparison."""
    string_names = set()
    for filename in filenames:
        try:
            normalized = normalize_filename(filename)
            string_names.add(normalized)
            safe_log("Added to current names: {}".format(normalized))
        except Exception as e:
            safe_log("Warning: Could not normalize filename {}: {}".format(filename, str(e)))
    return string_names

# And modify the comparison to:
def compare_with_string_set(image_name_rich, string_current_names):
    """Compare RichString with set of normalized strings."""
    try:
        if hasattr(image_name_rich, 'getString'):
            image_str = image_name_rich.getString()
        else:
            image_str = str(image_name_rich)
        
        normalized_image = normalize_filename(image_str)
        return normalized_image in string_current_names
        
    except Exception as e:
        safe_log("Warning: Could not process image name for string comparison: {}".format(str(e)))
        return False
