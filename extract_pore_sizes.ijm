args = getArgument();
argArray = split(args, ",");
pixels = 214;
microns = 50;

run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");
run("Set Scale...", "distance="+pixels+" known="+microns+" unit=µm global");
run("Set Measurements...", "area redirect=None decimal=3");
run("Analyze Particles...", "size=100-infinity minimum=50 show=Overlay add display exclude include clear record");

n = nResults;
	
deleted_entries = 0;

// excluding the scale bar
for (i=n-1; i>=0; i--) {
    if ((getResult('XStart', i)>2260) && (getResult('YStart', i)>1565))	{
		roiManager("select", i);
		roiManager("Delete");
		deleted_entries++;
	}
}
n = n-deleted_entries;


// =============================================================================
// MAIN ANALYSIS
// =============================================================================

// Arrays to store all pore data for final distribution
allPoreAreas = newArray(0);
allPoreParticleIDs = newArray(0);
totalPoreCount = 0;

// Get original image for pore extraction
originalID = getImageID();
originalTitle = getTitle();

/*print("\\Clear");
print("=== PORE SIZE EXTRACTION ANALYSIS ===");
print("Image: " + originalTitle);
print("Found " + n + " particles for pore analysis");
print("");
*/

// Process each particle to extract pores
for (i=0; i<n; i++) {
    //print("Processing particle " + (i+1) + " of " + n + "...");

    // Select the original image by ID (more reliable than title)
    selectImage(originalID);
    roiManager("select", i);

    // Create a working image for this particle
    run("Duplicate...", "title=Particle_" + (i+1));
    particleID = getImageID();

    // Clear outside the particle to isolate it
    run("Clear Outside");

    // Invert the image so pores (holes) become white particles
    run("Invert");

    // Analyze pores within this particle (don't add to ROI manager)
    run("Set Measurements...", "area redirect=None decimal=3");

    // Check if there are any white pixels before running Analyze Particles
    getRawStatistics(nPixels, mean, min, max, std, histogram);
    if (max > 0) {
        // Only run analysis if there are white pixels (potential pores)
        run("Analyze Particles...", "size=1-Infinity display exclude clear");
    } else {
        // No white pixels found, clear results to ensure nResults = 0
        run("Clear Results");
    }

    nPores = nResults;

    if (nPores > 0) {
        //print("  Found " + nPores + " pores in particle " + (i+1));

        // Store pore data for this particle
        for (j = 0; j < nPores; j++) {
            poreArea = getResult("Area", j);

            // Expand arrays to store new pore data
            allPoreAreas = Array.concat(allPoreAreas, poreArea);
            allPoreParticleIDs = Array.concat(allPoreParticleIDs, i+1);
            totalPoreCount++;

            //print("    Pore " + (j+1) + ": Area = " + d2s(poreArea, 3) + " µm²");
        }
    } //else {
        //print("  No pores found in particle " + (i+1));
    //}

    // Clean up - close the working image and clear results
    selectImage(particleID);
    close();
    run("Clear Results");
    
}

if (totalPoreCount > 0) {
    // Create detailed results table
    run("Clear Results");
    for (i = 0; i < totalPoreCount; i++) {
        setResult("Pore_ID", i, i+1);
        setResult("Particle_ID", i, allPoreParticleIDs[i]);
        setResult("Pore_Area_um2", i, allPoreAreas[i]);
    }
    updateResults();
}
