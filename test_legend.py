"""
Test script to verify legend creation functionality
This script creates a simple image with a legend overlay to test the coloring functionality.
"""

from ij import IJ, ImagePlus
from ij.gui import Roi, TextRoi, Overlay
from java.awt import Color, Font
from ij.process import ColorProcessor

# Color definitions (same as in main script)
DUAL_COLORS = {
    ("NonPorous", "Round"): Color(0, 255, 0, 100),      # Green
    ("NonPorous", "Imperfect"): Color(100, 0, 100, 100), # Purple
    ("Porous", "Round"): Color(255, 255, 0, 100),       # Yellow
    ("Porous", "Imperfect"): Color(255, 0, 0, 100)      # Red
}

def create_test_legend():
    """Create a test image with legend to verify coloring works."""
    
    # Create a simple test image
    width, height = 800, 600
    processor = ColorProcessor(width, height)
    processor.setColor(Color.WHITE)
    processor.fill()
    
    imp = ImagePlus("Legend Test", processor)
    imp.show()
    
    # Create overlay for legend
    overlay = Overlay()
    
    # Legend settings
    legend_x, legend_y = 10, 10
    legend_width, legend_height = 320, 200
    
    # Background
    bg_roi = Roi(legend_x, legend_y, legend_width, legend_height)
    bg_roi.setFillColor(Color(255, 255, 255, 180))
    bg_roi.setStrokeColor(Color.BLACK)
    overlay.add(bg_roi)
    
    # Title
    title_roi = TextRoi(legend_x + 10, legend_y + 10, "Classification Legend")
    title_roi.setCurrentFont(Font("SansSerif", Font.BOLD, 24))
    title_roi.setFillColor(Color.BLACK)
    overlay.add(title_roi)
    
    # Color entries
    y_offset, entry_height = 50, 36
    for i, (porosity_class, shape_class) in enumerate([
        ("NonPorous", "Round"), ("NonPorous", "Imperfect"),
        ("Porous", "Round"), ("Porous", "Imperfect")
    ]):
        # Get the base color and make it more opaque for the legend
        base_color = DUAL_COLORS.get((porosity_class, shape_class), Color.GRAY)
        # Create a more opaque version for the legend (alpha = 200 instead of 100)
        legend_color = Color(base_color.getRed(), base_color.getGreen(), base_color.getBlue(), 200)

        # Color square - create a filled rectangle that will definitely show color
        square_x = legend_x + 10
        square_y = legend_y + y_offset + i * entry_height
        square_size = 24
        
        # Create the main colored square with more opaque color
        color_roi = Roi(square_x, square_y, square_size, square_size)
        color_roi.setFillColor(legend_color)
        color_roi.setStrokeColor(legend_color)
        color_roi.setStrokeWidth(1)
        overlay.add(color_roi)
        
        # Add a black border around the square for definition
        border_roi = Roi(square_x, square_y, square_size, square_size)
        border_roi.setStrokeColor(Color.BLACK)
        border_roi.setStrokeWidth(2)
        overlay.add(border_roi)

        # Text label
        label_text = porosity_class + " + " + shape_class
        text_roi = TextRoi(legend_x + 44, legend_y + y_offset + i * entry_height + 4, label_text + " ")
        text_roi.setCurrentFont(Font("SansSerif", Font.PLAIN, 18))
        text_roi.setFillColor(Color.BLACK)
        overlay.add(text_roi)
    
    # Set overlay and update
    imp.setOverlay(overlay)
    imp.updateAndDraw()
    
    IJ.log("Legend test created. Check if colored squares are visible.")
    return imp

# Run the test
if __name__ == "__main__":
    IJ.log("\\Clear")
    IJ.log("Testing legend creation...")
    test_imp = create_test_legend()
    IJ.log("Test complete. Check the 'Legend Test' window for colored squares.")
