# Solution 3: RichString toString() Method Comparison
# Use the toString() method which should be more reliable for content comparison

def richstring_content_equals(rich1, rich2):
    """
    Compare two RichString objects by their content using toString() method.
    This is more reliable than object identity comparison.
    """
    try:
        # Use toString() method which should give consistent string representation
        str1 = rich1.toString() if rich1 else ""
        str2 = rich2.toString() if rich2 else ""
        return str1 == str2
    except Exception as e:
        safe_log("Warning: toString() comparison failed: {}".format(str(e)))
        return False

def richstring_in_set(target_rich, richstring_set):
    """
    Check if a RichString is equivalent to any RichString in a set using content comparison.
    """
    for rich_item in richstring_set:
        if richstring_content_equals(target_rich, rich_item):
            return True
    return False

# Alternative approach: Use the string content but keep RichString objects
def richstring_content_in_string_set(target_rich, string_set):
    """
    Check if RichString content matches any string in a string set.
    """
    try:
        target_str = target_rich.toString() if target_rich else ""
        return target_str in string_set
    except Exception as e:
        safe_log("Warning: RichString to string conversion failed: {}".format(str(e)))
        return False

# For the main processing, create a parallel string set:
def create_parallel_string_set(richstring_set):
    """
    Create a parallel set of strings from RichString objects for faster comparison.
    """
    string_set = set()
    for rich_obj in richstring_set:
        try:
            str_content = rich_obj.toString()
            string_set.add(str_content)
        except Exception as e:
            safe_log("Warning: Could not convert RichString to string: {}".format(str(e)))
    return string_set

# Modified comparison logic:
"""
Replace the comparison section in load_existing_pore_data_from_excel:

# Create parallel string set for faster comparison (do this once at the beginning)
if hasattr(current_image_names, '__iter__'):
    current_image_strings = create_parallel_string_set(current_image_names)
else:
    current_image_strings = set()

# Then in the main loop, replace line 225 with:
if richstring_content_in_string_set(image_name, current_image_strings):
    # ... rest of the logic
"""

# Complete solution for the main function modification:
def setup_richstring_with_string_parallel(filenames):
    """
    Setup both RichString objects and parallel string set for comparison.
    """
    from org.apache.poi.xssf.usermodel import XSSFRichTextString
    
    current_image_names = set()
    current_image_strings = set()
    
    for filename in filenames:
        try:
            # Create RichString object
            rich_filename = XSSFRichTextString(filename)
            current_image_names.add(rich_filename)
            
            # Create parallel string for comparison
            str_filename = rich_filename.toString()
            current_image_strings.add(str_filename)
            
            safe_log("Added current image: {} (RichString: {})".format(filename, str_filename))
            
        except Exception as e:
            safe_log("Warning: Could not process filename {}: {}".format(filename, str(e)))
    
    return current_image_names, current_image_strings

# Usage in main function:
"""
# Replace the current_image_names creation with:
current_image_names, current_image_strings = setup_richstring_with_string_parallel(filenames)

# Pass both to the Excel loading function or modify it to use the string set
"""
