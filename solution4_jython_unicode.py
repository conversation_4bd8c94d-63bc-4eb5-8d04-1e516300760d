# Solution 4: Jython-Specific Unicode Handling
# Addresses specific Jython 2.7 Unicode issues with special characters like µ

def safe_unicode_extract(rich_obj):
    """
    Extract Unicode string from RichString object with Jython 2.7 compatibility.
    Handles special characters like µ properly.
    """
    if not rich_obj:
        return ""
    
    try:
        # Method 1: Try getString() if available (most reliable)
        if hasattr(rich_obj, 'getString'):
            return rich_obj.getString()
    except:
        pass
    
    try:
        # Method 2: Try toString() 
        return rich_obj.toString()
    except:
        pass
    
    try:
        # Method 3: Direct string conversion
        return str(rich_obj)
    except UnicodeEncodeError:
        # Method 4: Handle Unicode encoding issues in Jython
        try:
            # Convert to Unicode first, then to UTF-8
            unicode_str = unicode(rich_obj)
            return unicode_str.encode('utf-8').decode('utf-8')
        except:
            pass
    except:
        pass
    
    # Method 5: Last resort - return a safe representation
    try:
        return repr(rich_obj)
    except:
        return "<unreadable>"

def normalize_for_jython_comparison(text):
    """
    Normalize text for comparison in Jython, handling Unicode characters.
    """
    if not text:
        return ""
    
    try:
        # Apply the existing normalize_filename function
        normalized = normalize_filename(text)
        
        # Additional Jython-specific normalization
        # Ensure consistent Unicode handling
        if isinstance(normalized, unicode):
            return normalized.encode('utf-8').decode('utf-8')
        else:
            return str(normalized)
            
    except Exception as e:
        safe_log("Warning: Normalization failed for '{}': {}".format(text, str(e)))
        # Fallback: just return the original text as string
        try:
            return str(text)
        except:
            return "<unnormalizable>"

def jython_safe_comparison(excel_rich, current_names_set):
    """
    Jython-safe comparison between RichString from Excel and set of current names.
    """
    try:
        # Extract string from Excel RichString
        excel_str = safe_unicode_extract(excel_rich)
        excel_normalized = normalize_for_jython_comparison(excel_str)
        
        safe_log("Comparing Excel image: '{}' (normalized: '{}')".format(excel_str, excel_normalized))
        
        # Compare with each item in current_names_set
        for current_rich in current_names_set:
            try:
                current_str = safe_unicode_extract(current_rich)
                current_normalized = normalize_for_jython_comparison(current_str)
                
                # Try multiple comparison methods
                if excel_str == current_str:
                    safe_log("Match found: exact string match")
                    return True
                    
                if excel_normalized == current_normalized:
                    safe_log("Match found: normalized match")
                    return True
                    
                # Also try case-insensitive comparison as fallback
                if excel_normalized.lower() == current_normalized.lower():
                    safe_log("Match found: case-insensitive match")
                    return True
                    
            except Exception as e:
                safe_log("Warning: Could not compare with current name: {}".format(str(e)))
                continue
        
        safe_log("No match found for: '{}'".format(excel_normalized))
        return False
        
    except Exception as e:
        safe_log("Error in jython_safe_comparison: {}".format(str(e)))
        return False

# Debug function to help understand what's happening
def debug_comparison_values(excel_rich, current_names_set):
    """
    Debug function to print all values for comparison analysis.
    """
    safe_log("=== DEBUG COMPARISON ===")
    
    try:
        excel_str = safe_unicode_extract(excel_rich)
        excel_norm = normalize_for_jython_comparison(excel_str)
        safe_log("Excel image: '{}' -> normalized: '{}'".format(excel_str, excel_norm))
        safe_log("Excel image type: {}".format(type(excel_rich)))
    except Exception as e:
        safe_log("Could not extract Excel image: {}".format(str(e)))
    
    safe_log("Current names in set:")
    for i, current_rich in enumerate(current_names_set):
        try:
            current_str = safe_unicode_extract(current_rich)
            current_norm = normalize_for_jython_comparison(current_str)
            safe_log("  [{}] '{}' -> normalized: '{}' (type: {})".format(
                i, current_str, current_norm, type(current_rich)))
        except Exception as e:
            safe_log("  [{}] <error extracting>: {}".format(i, str(e)))
    
    safe_log("=== END DEBUG ===")

# Usage in main function:
"""
Replace the comparison line 225 with:

# Optional: Add debug output
debug_comparison_values(image_name, current_image_names)

# Main comparison
if jython_safe_comparison(image_name, current_image_names):
    # ... existing logic
"""
