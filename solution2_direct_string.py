# Solution 2: Direct String Approach - Abandon RichString for Comparison
# Modify the main processing section (around line 1250) to use strings directly

# Replace the current_image_names creation with:
def create_normalized_string_set(filenames):
    """Create set of normalized strings, handling Unicode properly for <PERSON>yt<PERSON>."""
    normalized_names = set()
    
    for filename in filenames:
        try:
            # Normalize the filename to handle µ and other Unicode chars
            normalized = normalize_filename(filename)
            normalized_names.add(normalized)
            safe_log("Current image (normalized): {}".format(normalized))
        except Exception as e:
            safe_log("Warning: Could not normalize filename {}: {}".format(filename, str(e)))
            # Try adding original filename as fallback
            try:
                normalized_names.add(filename)
            except:
                pass
    
    return normalized_names

# In the Excel reading function, modify image name extraction:
def extract_and_normalize_excel_image_name(image_cell):
    """Extract image name from Excel cell and normalize it for comparison."""
    try:
        # Try RichString first (best for Unicode)
        raw_image_name = image_cell.getRichStringCellValue()
        if raw_image_name:
            # Convert RichString to string using getString() method if available
            if hasattr(raw_image_name, 'getString'):
                image_str = raw_image_name.getString()
            else:
                image_str = str(raw_image_name)
            
            # Normalize for comparison
            normalized = normalize_filename(image_str)
            return normalized, image_str  # Return both normalized and original
            
    except Exception as e:
        safe_log("RichString extraction failed: {} - trying string fallback".format(str(e)))
    
    try:
        # Fallback to regular string
        raw_image_name = image_cell.getStringCellValue()
        if raw_image_name:
            normalized = normalize_filename(raw_image_name)
            return normalized, raw_image_name
            
    except Exception as e:
        safe_log("String extraction failed: {}".format(str(e)))
    
    return None, None

# Modified comparison logic for the main function:
# Replace the image name reading section in load_existing_pore_data_from_excel with:

"""
# Step 1: Read and normalize image name
normalized_image_name, original_image_name = extract_and_normalize_excel_image_name(image_cell)

if not normalized_image_name:
    safe_log("Warning: Row {} has empty or unreadable image name - skipping".format(row_idx))
    continue

safe_log("Read image name: {} (normalized: {})".format(original_image_name, normalized_image_name))

# Later in the comparison:
if normalized_image_name in current_image_names:  # Now comparing strings with strings
    all_pore_data.append({
        "image": normalized_image_name,
        "particle_id": particle_id,
        "pore_area": pore_area
    })
    filtered_pore_count += 1
    safe_log("Preserved pore data: {} - Particle {} - Area {:.3f}".format(
        normalized_image_name, particle_id, pore_area))
else:
    safe_log("Filtered out pore data: {} (not in current directory)".format(normalized_image_name))
"""

# Complete replacement for the main processing section:
def setup_current_image_names_as_strings(filenames):
    """Setup current_image_names as normalized strings instead of RichString objects."""
    # Don't import RichString classes - use strings directly
    current_image_names = create_normalized_string_set(filenames)
    safe_log("Created {} normalized current image names".format(len(current_image_names)))
    return current_image_names
